# TecnoDrive Platform - سكريبت التشغيل الموحد
# Unified Startup Script for TecnoDrive Platform

param(
    [string]$Mode = "development",  # development, production, minimal
    [switch]$SkipDatabase,
    [switch]$SkipMicroservices,
    [switch]$SkipFrontend,
    [switch]$SkipComprehensive,
    [switch]$Help
)

if ($Help) {
    Write-Host "=== TecnoDrive Platform Startup Script ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\start-platform.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Mode <mode>           Startup mode: development, production, minimal (default: development)"
    Write-Host "  -SkipDatabase         Skip database startup"
    Write-Host "  -SkipMicroservices    Skip Java microservices"
    Write-Host "  -SkipFrontend         Skip frontend applications"
    Write-Host "  -SkipComprehensive    Skip comprehensive Python system"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\start-platform.ps1                    # Start all services in development mode"
    Write-Host "  .\start-platform.ps1 -Mode minimal      # Start only essential services"
    Write-Host "  .\start-platform.ps1 -SkipFrontend      # Start backend only"
    exit 0
}

Write-Host "🚗 Starting TecnoDrive Platform..." -ForegroundColor Green
Write-Host "Mode: $Mode" -ForegroundColor Yellow

# Function to check if port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $Port -WarningAction SilentlyContinue
        return $connection.TcpTestSucceeded
    } catch {
        return $false
    }
}

# Function to wait for service
function Wait-ForService {
    param([string]$ServiceName, [int]$Port, [int]$TimeoutSeconds = 60)
    
    Write-Host "⏳ Waiting for $ServiceName on port $Port..." -ForegroundColor Yellow
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    
    while ((Get-Date) -lt $timeout) {
        if (Test-Port -Port $Port) {
            Write-Host "✅ $ServiceName is ready!" -ForegroundColor Green
            return $true
        }
        Start-Sleep -Seconds 2
    }
    
    Write-Host "❌ Timeout waiting for $ServiceName" -ForegroundColor Red
    return $false
}

# 1. Start Database Services
if (-not $SkipDatabase) {
    Write-Host "`n📊 Starting Database Services..." -ForegroundColor Cyan
    
    # Check if PostgreSQL is running
    if (-not (Test-Port -Port 5433)) {
        Write-Host "Starting PostgreSQL..." -ForegroundColor Yellow
        Set-Location infrastructure/docker
        docker-compose up -d postgres
        Set-Location ../..
        Wait-ForService -ServiceName "PostgreSQL" -Port 5433
    } else {
        Write-Host "✅ PostgreSQL already running" -ForegroundColor Green
    }
    
    # Check if Redis is running
    if (-not (Test-Port -Port 6379)) {
        Write-Host "Starting Redis..." -ForegroundColor Yellow
        Set-Location infrastructure/docker
        docker-compose up -d redis
        Set-Location ../..
        Wait-ForService -ServiceName "Redis" -Port 6379
    } else {
        Write-Host "✅ Redis already running" -ForegroundColor Green
    }
}

# 2. Start Java Microservices
if (-not $SkipMicroservices) {
    Write-Host "`n🔧 Starting Java Microservices..." -ForegroundColor Cyan
    
    # Start Eureka Server
    if (-not (Test-Port -Port 8761)) {
        Write-Host "Starting Eureka Server..." -ForegroundColor Yellow
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'backend/microservices/infrastructure/eureka-server'; mvn spring-boot:run"
        Wait-ForService -ServiceName "Eureka Server" -Port 8761
    } else {
        Write-Host "✅ Eureka Server already running" -ForegroundColor Green
    }
    
    # Start API Gateway
    if (-not (Test-Port -Port 8080)) {
        Write-Host "Starting API Gateway..." -ForegroundColor Yellow
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'backend/microservices/infrastructure/api-gateway'; mvn spring-boot:run"
        Wait-ForService -ServiceName "API Gateway" -Port 8080
    } else {
        Write-Host "✅ API Gateway already running" -ForegroundColor Green
    }
    
    # Start Core Services
    $coreServices = @(
        @{Name="User Service"; Port=8083; Path="backend/microservices/core/user-service"},
        @{Name="Auth Service"; Port=8081; Path="backend/microservices/core/auth-service"},
        @{Name="Payment Service"; Port=8086; Path="backend/microservices/core/payment-service"}
    )
    
    if ($Mode -ne "minimal") {
        $coreServices += @(
            @{Name="Ride Service"; Port=8082; Path="backend/microservices/business/ride-service"},
            @{Name="Fleet Service"; Port=8084; Path="backend/microservices/business/fleet-service"},
            @{Name="Location Service"; Port=8085; Path="backend/microservices/business/location-service"}
        )
    }
    
    foreach ($service in $coreServices) {
        if (-not (Test-Port -Port $service.Port)) {
            Write-Host "Starting $($service.Name)..." -ForegroundColor Yellow
            Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$($service.Path)'; mvn spring-boot:run"
            Start-Sleep -Seconds 5  # Stagger startup
        } else {
            Write-Host "✅ $($service.Name) already running" -ForegroundColor Green
        }
    }
}

# 3. Start Comprehensive Python System
if (-not $SkipComprehensive) {
    Write-Host "`n🐍 Starting Comprehensive Python System..." -ForegroundColor Cyan
    
    if (-not (Test-Port -Port 8000)) {
        Write-Host "Starting FastAPI Comprehensive System..." -ForegroundColor Yellow
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'backend/comprehensive-system'; if (Test-Path '.venv/Scripts/activate.ps1') { .venv/Scripts/activate.ps1 } else { python -m venv .venv; .venv/Scripts/activate.ps1; pip install -r requirements-basic.txt }; python main.py"
        Wait-ForService -ServiceName "Comprehensive System" -Port 8000 -TimeoutSeconds 120
    } else {
        Write-Host "✅ Comprehensive System already running" -ForegroundColor Green
    }
}

# 4. Start Frontend Applications
if (-not $SkipFrontend) {
    Write-Host "`n🌐 Starting Frontend Applications..." -ForegroundColor Cyan
    
    # Start Admin Dashboard
    if (-not (Test-Port -Port 3000)) {
        Write-Host "Starting Admin Dashboard..." -ForegroundColor Yellow
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'frontend/admin-dashboard'; if (-not (Test-Path 'node_modules')) { npm install }; npm start"
        Wait-ForService -ServiceName "Admin Dashboard" -Port 3000 -TimeoutSeconds 120
    } else {
        Write-Host "✅ Admin Dashboard already running" -ForegroundColor Green
    }
}

# 5. Display Status Summary
Write-Host "`n📋 Platform Status Summary:" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

$services = @(
    @{Name="PostgreSQL"; Port=5433; URL="postgresql://localhost:5433"},
    @{Name="Redis"; Port=6379; URL="redis://localhost:6379"},
    @{Name="Eureka Server"; Port=8761; URL="http://localhost:8761"},
    @{Name="API Gateway"; Port=8080; URL="http://localhost:8080"},
    @{Name="User Service"; Port=8083; URL="http://localhost:8083/actuator/health"},
    @{Name="Auth Service"; Port=8081; URL="http://localhost:8081/actuator/health"},
    @{Name="Comprehensive System"; Port=8000; URL="http://localhost:8000/docs"},
    @{Name="Admin Dashboard"; Port=3000; URL="http://localhost:3000"}
)

foreach ($service in $services) {
    $status = if (Test-Port -Port $service.Port) { "✅ Running" } else { "❌ Stopped" }
    $color = if (Test-Port -Port $service.Port) { "Green" } else { "Red" }
    Write-Host "$($service.Name.PadRight(20)) $status - $($service.URL)" -ForegroundColor $color
}

Write-Host "`n🎉 TecnoDrive Platform startup completed!" -ForegroundColor Green
Write-Host "📖 Access the documentation at: http://localhost:8000/docs" -ForegroundColor Yellow
Write-Host "🎛️ Access the admin dashboard at: http://localhost:3000" -ForegroundColor Yellow
Write-Host "🔍 Monitor services at: http://localhost:8761" -ForegroundColor Yellow

if ($Mode -eq "development") {
    Write-Host "`n💡 Development Tips:" -ForegroundColor Cyan
    Write-Host "- Use Ctrl+C to stop individual services"
    Write-Host "- Check logs in each PowerShell window"
    Write-Host "- API Gateway routes all requests through port 8080"
    Write-Host "- Database connection: localhost:5433/tecnodrive"
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
